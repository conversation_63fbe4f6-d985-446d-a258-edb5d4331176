import GbTabbar from "../../components/GbTabbar";
import GbNavbar from "../../components/GbNavbar";
import GbBody from "../../components/GbBody";
import GbPage from "../../components/GbPage";
import styles from "./index.module.scss";
import { GbAvatarGroup } from "../../components/GbAvatar";
import { Add } from "@nutui/icons-react-taro";
import GbButton from "../../components/GbButton";
import BbFunctionCard from "../../components/Bb/BbFunctionCard";

const Index = () => {
	return (
		<GbPage className={styles.root}>
			<GbBody hasTabbar inNavbar full>
				<GbNavbar title="首页" background="transparent" />
				<GbAvatarGroup
					style={{ margin: "80rpx auto" }}
					max={2}
					list={[
						{
							src: "https://img1.baidu.com/it/u=1112506223,3000000000&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500",
						},
						{
							icon: <Add />,
						},
					]}
				/>
				<GbButton
					style={{
						margin: "0 auto",
						borderRadius: "999rpx !important",
						paddingLeft: "80rpx !important",
						paddingRight: "80rpx !important",
						background: "var(--app-primary-color)",
					}}
				>
					邀请另一半开通情侣空间
				</GbButton>
				<BbFunctionCard />
			</GbBody>
			<GbTabbar value={0} />
		</GbPage>
	);
};

export default Index;
